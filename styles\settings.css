/* إعدادات Anime Paradise */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Noto Sans Arabic', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
    direction: rtl;
}

.settings-container {
    max-width: 800px;
    margin: 0 auto;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    margin-top: 20px;
    margin-bottom: 20px;
}

/* الهيدر */
.settings-header {
    background: linear-gradient(135deg, #ff6b9d, #4ecdc4);
    color: white;
    padding: 30px;
    text-align: center;
}

.settings-header h1 {
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.settings-header p {
    font-size: 16px;
    opacity: 0.9;
}

/* المحتوى */
.settings-content {
    padding: 30px;
}

.settings-section {
    margin-bottom: 40px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 15px;
    padding: 25px;
    border: 1px solid rgba(255, 182, 193, 0.3);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.settings-section h2 {
    font-size: 20px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid rgba(255, 182, 193, 0.3);
}

.setting-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
    padding: 15px;
    background: rgba(255, 255, 255, 0.6);
    border-radius: 10px;
    border: 1px solid rgba(255, 182, 193, 0.2);
    transition: all 0.3s ease;
}

.setting-item:hover {
    background: rgba(255, 255, 255, 0.8);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 182, 193, 0.2);
}

.setting-item label {
    font-weight: 500;
    color: #555;
    flex: 1;
    margin-left: 15px;
}

/* عناصر الإدخال */
input[type="checkbox"] {
    width: 20px;
    height: 20px;
    accent-color: #ff6b9d;
    cursor: pointer;
}

input[type="range"] {
    width: 150px;
    height: 6px;
    background: linear-gradient(90deg, #ff6b9d, #4ecdc4);
    border-radius: 3px;
    outline: none;
    cursor: pointer;
}

input[type="range"]::-webkit-slider-thumb {
    appearance: none;
    width: 20px;
    height: 20px;
    background: white;
    border-radius: 50%;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    cursor: pointer;
}

input[type="number"] {
    width: 80px;
    padding: 8px 12px;
    border: 2px solid rgba(255, 182, 193, 0.3);
    border-radius: 8px;
    background: white;
    font-size: 14px;
    text-align: center;
}

input[type="number"]:focus {
    border-color: #ff6b9d;
    outline: none;
    box-shadow: 0 0 10px rgba(255, 107, 157, 0.3);
}

input[type="color"] {
    width: 50px;
    height: 40px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    background: none;
}

select {
    padding: 8px 15px;
    border: 2px solid rgba(255, 182, 193, 0.3);
    border-radius: 8px;
    background: white;
    font-size: 14px;
    cursor: pointer;
    min-width: 120px;
}

select:focus {
    border-color: #ff6b9d;
    outline: none;
    box-shadow: 0 0 10px rgba(255, 107, 157, 0.3);
}

.range-value {
    background: #ff6b9d;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
    margin-right: 10px;
    min-width: 30px;
    text-align: center;
}

/* الفوتر */
.settings-footer {
    background: rgba(255, 255, 255, 0.9);
    padding: 25px 30px;
    border-top: 1px solid rgba(255, 182, 193, 0.3);
}

.button-group {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.btn {
    padding: 12px 25px;
    border: none;
    border-radius: 25px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background: linear-gradient(45deg, #ff6b9d, #4ecdc4);
    color: white;
    box-shadow: 0 5px 15px rgba(255, 107, 157, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 107, 157, 0.4);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.8);
    color: #666;
    border: 2px solid rgba(255, 182, 193, 0.3);
}

.btn-secondary:hover {
    background: rgba(255, 182, 193, 0.1);
    border-color: #ff6b9d;
    color: #ff6b9d;
    transform: translateY(-2px);
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s ease;
}

.btn:hover::before {
    left: 100%;
}

.settings-info {
    text-align: center;
    color: #666;
    font-size: 12px;
    line-height: 1.5;
}

/* تأثيرات الحركة */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.settings-section {
    animation: fadeIn 0.6s ease forwards;
}

.settings-section:nth-child(2) { animation-delay: 0.1s; }
.settings-section:nth-child(3) { animation-delay: 0.2s; }
.settings-section:nth-child(4) { animation-delay: 0.3s; }
.settings-section:nth-child(5) { animation-delay: 0.4s; }
.settings-section:nth-child(6) { animation-delay: 0.5s; }

/* تأثيرات الاستجابة */
@media (max-width: 768px) {
    .settings-container {
        margin: 10px;
        border-radius: 15px;
    }
    
    .settings-content {
        padding: 20px;
    }
    
    .settings-section {
        padding: 20px;
    }
    
    .setting-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .setting-item label {
        margin-left: 0;
        margin-bottom: 5px;
    }
    
    .button-group {
        flex-direction: column;
        align-items: center;
    }
    
    .btn {
        width: 100%;
        max-width: 200px;
    }
}

/* تأثيرات إضافية */
.setting-item input:focus,
.setting-item select:focus {
    animation: glow 0.3s ease;
}

@keyframes glow {
    0% { box-shadow: 0 0 5px rgba(255, 107, 157, 0.3); }
    50% { box-shadow: 0 0 20px rgba(255, 107, 157, 0.6); }
    100% { box-shadow: 0 0 5px rgba(255, 107, 157, 0.3); }
}

/* تأثير التحميل */
.loading {
    position: relative;
    pointer-events: none;
    opacity: 0.7;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #ff6b9d;
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
