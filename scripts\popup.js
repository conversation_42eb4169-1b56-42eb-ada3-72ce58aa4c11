// متغيرات عامة
let currentWallpaperIndex = 0;
let animationPaused = false;
let currentQuoteIndex = 0;

// الخلفيات المتحركة
const wallpapers = [
    'linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #ffeaa7)',
    'linear-gradient(45deg, #667eea, #764ba2, #f093fb, #f5576c)',
    'linear-gradient(45deg, #4facfe, #00f2fe, #43e97b, #38f9d7)',
    'linear-gradient(45deg, #fa709a, #fee140, #ff9a9e, #fecfef)',
    'linear-gradient(45deg, #a8edea, #fed6e3, #ffecd2, #fcb69f)'
];

// الاقتباسات الملهمة
const quotes = [
    {
        text: "الأحلام لا تصبح حقيقة من خلال السحر؛ إنها تحتاج إلى العرق والتصميم والعمل الشاق",
        author: "كولين باول"
    },
    {
        text: "لا تستسلم أبداً، لأن هذا هو المكان والوقت الذي ستتغير فيه الأمور",
        author: "هارييت بيتشر ستو"
    },
    {
        text: "النجاح ليس نهائياً، والفشل ليس قاتلاً: إنها الشجاعة للاستمرار هي التي تهم",
        author: "ونستون تشرشل"
    },
    {
        text: "الطريقة الوحيدة للقيام بعمل عظيم هي أن تحب ما تفعله",
        author: "ستيف جوبز"
    },
    {
        text: "المستقبل ينتمي لأولئك الذين يؤمنون بجمال أحلامهم",
        author: "إليانور روزفلت"
    }
];

// تهيئة التطبيق
document.addEventListener('DOMContentLoaded', function() {
    initializeNavigation();
    initializeWallpaperControls();
    initializeQuotes();
    initializeSettings();
    startBackgroundAnimations();
});

// تهيئة التنقل
function initializeNavigation() {
    const navButtons = document.querySelectorAll('.nav-btn');
    const sections = document.querySelectorAll('.section');

    navButtons.forEach(button => {
        button.addEventListener('click', function() {
            const targetSection = this.getAttribute('data-section');
            
            // إزالة الفئة النشطة من جميع الأزرار والأقسام
            navButtons.forEach(btn => btn.classList.remove('active'));
            sections.forEach(section => section.classList.remove('active'));
            
            // إضافة الفئة النشطة للزر والقسم المحدد
            this.classList.add('active');
            document.getElementById(targetSection).classList.add('active');
            
            // تأثير صوتي (اختياري)
            playClickSound();
        });
    });
}

// تهيئة أزرار التحكم في الخلفيات
function initializeWallpaperControls() {
    const changeWallpaperBtn = document.getElementById('changeWallpaper');
    const toggleAnimationBtn = document.getElementById('toggleAnimation');

    changeWallpaperBtn.addEventListener('click', function() {
        changeWallpaper();
        this.style.transform = 'scale(0.95)';
        setTimeout(() => {
            this.style.transform = 'scale(1)';
        }, 150);
    });

    toggleAnimationBtn.addEventListener('click', function() {
        toggleAnimation();
        this.style.transform = 'scale(0.95)';
        setTimeout(() => {
            this.style.transform = 'scale(1)';
        }, 150);
    });
}

// تغيير الخلفية
function changeWallpaper() {
    currentWallpaperIndex = (currentWallpaperIndex + 1) % wallpapers.length;
    const background = document.querySelector('.animated-background');
    
    background.style.background = wallpapers[currentWallpaperIndex];
    background.style.backgroundSize = '400% 400%';
    
    // تأثير انتقال سلس
    background.style.opacity = '0.8';
    setTimeout(() => {
        background.style.opacity = '1';
    }, 300);
}

// تبديل الحركة
function toggleAnimation() {
    const background = document.querySelector('.animated-background');
    const particles = document.querySelector('.floating-particles');
    const toggleBtn = document.getElementById('toggleAnimation');
    
    animationPaused = !animationPaused;
    
    if (animationPaused) {
        background.style.animationPlayState = 'paused';
        particles.style.animationPlayState = 'paused';
        toggleBtn.innerHTML = '<span>▶️</span>تشغيل الحركة';
    } else {
        background.style.animationPlayState = 'running';
        particles.style.animationPlayState = 'running';
        toggleBtn.innerHTML = '<span>⏸️</span>إيقاف الحركة';
    }
}

// تهيئة الاقتباسات
function initializeQuotes() {
    const newQuoteBtn = document.getElementById('newQuote');
    
    newQuoteBtn.addEventListener('click', function() {
        showNewQuote();
        this.style.transform = 'scale(0.95)';
        setTimeout(() => {
            this.style.transform = 'scale(1)';
        }, 150);
    });
    
    // عرض اقتباس عشوائي عند البداية
    showNewQuote();
}

// عرض اقتباس جديد
function showNewQuote() {
    currentQuoteIndex = Math.floor(Math.random() * quotes.length);
    const quote = quotes[currentQuoteIndex];
    
    const quoteElement = document.getElementById('currentQuote');
    const authorElement = document.getElementById('quoteAuthor');
    
    // تأثير انتقال
    quoteElement.style.opacity = '0';
    authorElement.style.opacity = '0';
    
    setTimeout(() => {
        quoteElement.textContent = `"${quote.text}"`;
        authorElement.textContent = `- ${quote.author}`;
        
        quoteElement.style.opacity = '1';
        authorElement.style.opacity = '1';
    }, 300);
}

// تهيئة الإعدادات
function initializeSettings() {
    const settingsBtn = document.getElementById('settingsBtn');
    
    settingsBtn.addEventListener('click', function() {
        showSettingsModal();
    });
}

// عرض نافذة الإعدادات
function showSettingsModal() {
    // يمكن إضافة نافذة إعدادات منبثقة هنا
    alert('إعدادات التطبيق - قريباً!');
}

// بدء الحركات الخلفية
function startBackgroundAnimations() {
    // تحريك الجسيمات العائمة
    createFloatingElements();
    
    // تغيير الخلفية تلقائياً كل 30 ثانية
    setInterval(() => {
        if (!animationPaused) {
            changeWallpaper();
        }
    }, 30000);
    
    // تغيير الاقتباس تلقائياً كل 45 ثانية
    setInterval(() => {
        showNewQuote();
    }, 45000);
}

// إنشاء عناصر عائمة إضافية
function createFloatingElements() {
    const container = document.querySelector('.container');
    
    for (let i = 0; i < 5; i++) {
        const element = document.createElement('div');
        element.className = 'floating-element';
        element.style.cssText = `
            position: absolute;
            width: ${Math.random() * 10 + 5}px;
            height: ${Math.random() * 10 + 5}px;
            background: rgba(255, 255, 255, ${Math.random() * 0.3 + 0.1});
            border-radius: 50%;
            left: ${Math.random() * 100}%;
            top: ${Math.random() * 100}%;
            animation: floatRandom ${Math.random() * 10 + 10}s linear infinite;
            z-index: 1;
            pointer-events: none;
        `;
        
        container.appendChild(element);
    }
    
    // إضافة CSS للحركة العشوائية
    const style = document.createElement('style');
    style.textContent = `
        @keyframes floatRandom {
            0% { transform: translate(0, 0) rotate(0deg); opacity: 0; }
            10% { opacity: 1; }
            90% { opacity: 1; }
            100% { transform: translate(${Math.random() * 200 - 100}px, ${Math.random() * 200 - 100}px) rotate(360deg); opacity: 0; }
        }
    `;
    document.head.appendChild(style);
}

// تأثير صوتي للنقرات (اختياري)
function playClickSound() {
    // يمكن إضافة تأثير صوتي هنا إذا رغبت
    // const audio = new Audio('sounds/click.mp3');
    // audio.play().catch(e => console.log('Sound not available'));
}

// تأثيرات إضافية للتفاعل
document.addEventListener('mousemove', function(e) {
    const cursor = document.querySelector('.cursor-effect');
    if (!cursor) {
        const cursorElement = document.createElement('div');
        cursorElement.className = 'cursor-effect';
        cursorElement.style.cssText = `
            position: fixed;
            width: 20px;
            height: 20px;
            background: radial-gradient(circle, rgba(255,255,255,0.3) 0%, transparent 70%);
            border-radius: 50%;
            pointer-events: none;
            z-index: 9999;
            transition: transform 0.1s ease;
        `;
        document.body.appendChild(cursorElement);
    }
    
    const cursorEffect = document.querySelector('.cursor-effect');
    cursorEffect.style.left = e.clientX - 10 + 'px';
    cursorEffect.style.top = e.clientY - 10 + 'px';
});

// حفظ واستعادة الإعدادات
function saveSettings() {
    const settings = {
        currentWallpaper: currentWallpaperIndex,
        animationPaused: animationPaused
    };
    
    chrome.storage.local.set({ animeSettings: settings });
}

function loadSettings() {
    chrome.storage.local.get(['animeSettings'], function(result) {
        if (result.animeSettings) {
            currentWallpaperIndex = result.animeSettings.currentWallpaper || 0;
            animationPaused = result.animeSettings.animationPaused || false;
            
            // تطبيق الإعدادات المحفوظة
            changeWallpaper();
            if (animationPaused) {
                toggleAnimation();
            }
        }
    });
}

// حفظ الإعدادات عند إغلاق النافذة
window.addEventListener('beforeunload', saveSettings);

// تحميل الإعدادات عند بدء التطبيق
document.addEventListener('DOMContentLoaded', loadSettings);

// إضافة المزيد من التفاعلات
function addAdvancedInteractions() {
    // تأثير الهز للبطاقات
    const characterCards = document.querySelectorAll('.character-card');
    characterCards.forEach(card => {
        card.addEventListener('click', function() {
            this.style.animation = 'shake 0.5s ease-in-out';
            setTimeout(() => {
                this.style.animation = '';
            }, 500);
        });
    });

    // تأثير النبضة للإحصائيات
    const statNumbers = document.querySelectorAll('.stat-number');
    statNumbers.forEach(stat => {
        stat.addEventListener('mouseenter', function() {
            this.style.animation = 'pulse 1s ease-in-out infinite';
        });
        stat.addEventListener('mouseleave', function() {
            this.style.animation = '';
        });
    });
}

// إضافة CSS للحركات الجديدة
const additionalStyles = document.createElement('style');
additionalStyles.textContent = `
    @keyframes shake {
        0%, 100% { transform: translateX(0); }
        25% { transform: translateX(-5px); }
        75% { transform: translateX(5px); }
    }

    @keyframes pulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.1); }
    }
`;
document.head.appendChild(additionalStyles);

// تشغيل التفاعلات المتقدمة
addAdvancedInteractions();
