{"name": "anime-paradise-extension", "version": "1.0.0", "description": "تجربة أنمي فريدة مع خلفيات متحركة جميلة وتصميم أرستقراطي أنيق لمتصفح جوجل كروم", "main": "background.js", "scripts": {"build": "npm run clean && npm run copy-files", "clean": "<PERSON><PERSON><PERSON> dist", "copy-files": "cpx \"**/*\" dist --exclude=\"node_modules/**\" --exclude=\"package*.json\" --exclude=\"*.md\"", "dev": "npm run build && npm run watch", "watch": "chokidar \"**/*\" -c \"npm run build\" --ignore \"node_modules/**\" --ignore \"dist/**\"", "lint": "eslint *.js scripts/*.js", "lint:fix": "eslint *.js scripts/*.js --fix", "test": "echo \"No tests specified yet\"", "zip": "npm run build && cd dist && zip -r ../anime-paradise-extension.zip .", "validate": "web-ext lint --source-dir=.", "start": "web-ext run --source-dir=."}, "keywords": ["anime", "chrome-extension", "wallpaper", "animation", "japanese", "manga", "otaku", "browser-extension", "أنمي", "خلفيات", "متحرك"], "author": {"name": "Anime Paradise Team", "email": "<EMAIL>", "url": "https://anime-paradise.com"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-username/anime-paradise-extension.git"}, "bugs": {"url": "https://github.com/your-username/anime-paradise-extension/issues"}, "homepage": "https://github.com/your-username/anime-paradise-extension#readme", "devDependencies": {"chokidar-cli": "^3.0.0", "cpx": "^1.5.0", "eslint": "^8.0.0", "rimraf": "^3.0.2", "web-ext": "^7.0.0"}, "engines": {"node": ">=14.0.0", "npm": ">=6.0.0"}, "browserslist": ["Chrome >= 88"], "manifest": {"version": "1.0.0", "name": "Anime Paradise - أنمي بارادايس", "description": "تجربة أنمي فريدة مع خلفيات متحركة جميلة وتصميم أرستقراطي أنيق"}, "chrome": {"minimum_version": "88"}, "permissions": ["storage", "activeTab"], "optional_permissions": ["notifications", "background"], "content_security_policy": {"extension_pages": "script-src 'self'; object-src 'self'"}, "web_accessible_resources": [{"resources": ["assets/*", "animations/*", "styles/*"], "matches": ["<all_urls>"]}], "categories": ["Entertainment", "Productivity", "Art & Design"], "tags": ["anime", "wallpaper", "animation", "japanese", "entertainment", "art", "design", "customization"], "screenshots": [{"src": "screenshots/main-interface.png", "type": "image/png", "sizes": "1280x800"}, {"src": "screenshots/characters-section.png", "type": "image/png", "sizes": "1280x800"}, {"src": "screenshots/wallpapers-section.png", "type": "image/png", "sizes": "1280x800"}, {"src": "screenshots/settings-page.png", "type": "image/png", "sizes": "1280x800"}], "funding": {"type": "github", "url": "https://github.com/sponsors/your-username"}, "contributors": [{"name": "المطور الرئيسي", "email": "<EMAIL>", "role": "Lead Developer"}], "config": {"development": {"enableDebug": true, "enableConsoleLogging": true, "enablePerformanceMonitoring": true}, "production": {"enableDebug": false, "enableConsoleLogging": false, "enablePerformanceMonitoring": false}}, "eslintConfig": {"env": {"browser": true, "es2021": true, "webextensions": true}, "extends": ["eslint:recommended"], "parserOptions": {"ecmaVersion": 12, "sourceType": "module"}, "rules": {"no-unused-vars": "warn", "no-console": "off", "prefer-const": "error", "no-var": "error"}, "globals": {"chrome": "readonly"}}, "prettier": {"semi": true, "trailingComma": "es5", "singleQuote": true, "printWidth": 80, "tabWidth": 2}}