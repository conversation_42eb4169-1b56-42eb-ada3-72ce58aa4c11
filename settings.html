<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعدادات Anime Paradise</title>
    <link rel="stylesheet" href="styles/settings.css">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="settings-container">
        <header class="settings-header">
            <h1>⚙️ إعدادات Anime Paradise</h1>
            <p>خصص تجربتك مع الأنمي</p>
        </header>

        <main class="settings-content">
            <!-- إعدادات الخلفية -->
            <section class="settings-section">
                <h2>🎨 إعدادات الخلفية</h2>
                <div class="setting-item">
                    <label for="enableBackgrounds">تفعيل الخلفيات المتحركة</label>
                    <input type="checkbox" id="enableBackgrounds" checked>
                </div>
                <div class="setting-item">
                    <label for="backgroundSpeed">سرعة الحركة</label>
                    <input type="range" id="backgroundSpeed" min="1" max="10" value="5">
                    <span class="range-value">5</span>
                </div>
                <div class="setting-item">
                    <label for="autoChangeInterval">تغيير تلقائي كل (ثانية)</label>
                    <input type="number" id="autoChangeInterval" min="10" max="300" value="30">
                </div>
            </section>

            <!-- إعدادات التأثيرات -->
            <section class="settings-section">
                <h2>✨ إعدادات التأثيرات</h2>
                <div class="setting-item">
                    <label for="enableParticles">تفعيل الجسيمات العائمة</label>
                    <input type="checkbox" id="enableParticles" checked>
                </div>
                <div class="setting-item">
                    <label for="particleDensity">كثافة الجسيمات</label>
                    <input type="range" id="particleDensity" min="1" max="20" value="10">
                    <span class="range-value">10</span>
                </div>
                <div class="setting-item">
                    <label for="enableCursorGlow">تفعيل توهج المؤشر</label>
                    <input type="checkbox" id="enableCursorGlow" checked>
                </div>
                <div class="setting-item">
                    <label for="enableClickWaves">تفعيل موجات النقر</label>
                    <input type="checkbox" id="enableClickWaves" checked>
                </div>
            </section>

            <!-- إعدادات المحتوى -->
            <section class="settings-section">
                <h2>📝 إعدادات المحتوى</h2>
                <div class="setting-item">
                    <label for="quoteChangeInterval">تغيير الاقتباسات كل (ثانية)</label>
                    <input type="number" id="quoteChangeInterval" min="30" max="600" value="45">
                </div>
                <div class="setting-item">
                    <label for="enableNotifications">تفعيل الإشعارات</label>
                    <input type="checkbox" id="enableNotifications">
                </div>
                <div class="setting-item">
                    <label for="language">اللغة</label>
                    <select id="language">
                        <option value="ar">العربية</option>
                        <option value="en">English</option>
                        <option value="ja">日本語</option>
                    </select>
                </div>
            </section>

            <!-- إعدادات الألوان -->
            <section class="settings-section">
                <h2>🎨 إعدادات الألوان</h2>
                <div class="setting-item">
                    <label for="colorTheme">نمط الألوان</label>
                    <select id="colorTheme">
                        <option value="default">افتراضي</option>
                        <option value="sakura">زهر الكرز</option>
                        <option value="ocean">المحيط</option>
                        <option value="sunset">غروب الشمس</option>
                        <option value="galaxy">المجرة</option>
                    </select>
                </div>
                <div class="setting-item">
                    <label for="primaryColor">اللون الأساسي</label>
                    <input type="color" id="primaryColor" value="#ff6b9d">
                </div>
                <div class="setting-item">
                    <label for="secondaryColor">اللون الثانوي</label>
                    <input type="color" id="secondaryColor" value="#4ecdc4">
                </div>
            </section>

            <!-- إعدادات الأداء -->
            <section class="settings-section">
                <h2>⚡ إعدادات الأداء</h2>
                <div class="setting-item">
                    <label for="enableWebEffects">تفعيل التأثيرات على المواقع</label>
                    <input type="checkbox" id="enableWebEffects" checked>
                </div>
                <div class="setting-item">
                    <label for="performanceMode">وضع الأداء</label>
                    <select id="performanceMode">
                        <option value="high">عالي الجودة</option>
                        <option value="balanced">متوازن</option>
                        <option value="performance">أداء عالي</option>
                    </select>
                </div>
                <div class="setting-item">
                    <label for="enableBlur">تفعيل تأثير الضبابية</label>
                    <input type="checkbox" id="enableBlur" checked>
                </div>
            </section>
        </main>

        <footer class="settings-footer">
            <div class="button-group">
                <button id="resetSettings" class="btn btn-secondary">إعادة تعيين</button>
                <button id="exportSettings" class="btn btn-secondary">تصدير الإعدادات</button>
                <button id="importSettings" class="btn btn-secondary">استيراد الإعدادات</button>
                <button id="saveSettings" class="btn btn-primary">حفظ الإعدادات</button>
            </div>
            <div class="settings-info">
                <p>Anime Paradise Extension v1.0.0</p>
                <p>تم التطوير بـ ❤️ للمجتمع العربي</p>
            </div>
        </footer>
    </div>

    <script src="scripts/settings.js"></script>
</body>
</html>
