<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار Anime Paradise Extension</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
            min-height: 100vh;
            margin: 0;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
        }
        h1 {
            font-size: 2.5em;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        .instructions {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 30px;
            margin: 20px 0;
            text-align: right;
            line-height: 1.8;
        }
        .step {
            margin: 15px 0;
            padding: 15px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            border-right: 4px solid #ff6b9d;
        }
        .highlight {
            background: linear-gradient(45deg, #ff6b9d, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            font-weight: bold;
        }
        .button {
            display: inline-block;
            background: linear-gradient(45deg, #ff6b9d, #4ecdc4);
            color: white;
            padding: 15px 30px;
            border-radius: 25px;
            text-decoration: none;
            margin: 10px;
            transition: transform 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        .button:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .feature {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
        }
        .feature-icon {
            font-size: 3em;
            margin-bottom: 10px;
        }
        .code-block {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            text-align: left;
            margin: 15px 0;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌸 مرحباً بك في Anime Paradise Extension</h1>
        
        <div class="instructions">
            <h2>📋 تعليمات التثبيت والاختبار</h2>
            
            <div class="step">
                <strong>الخطوة 1:</strong> افتح متصفح جوجل كروم واذهب إلى:
                <div class="code-block">chrome://extensions/</div>
            </div>
            
            <div class="step">
                <strong>الخطوة 2:</strong> فعّل <span class="highlight">"وضع المطور"</span> في أعلى يمين الصفحة
            </div>
            
            <div class="step">
                <strong>الخطوة 3:</strong> اضغط على <span class="highlight">"تحميل extension غير مُعبأ"</span>
            </div>
            
            <div class="step">
                <strong>الخطوة 4:</strong> اختر مجلد المشروع الحالي:
                <div class="code-block">c:\Users\<USER>\Desktop\ff</div>
            </div>
            
            <div class="step">
                <strong>الخطوة 5:</strong> ستظهر أيقونة Extension في شريط الأدوات - اضغط عليها!
            </div>
        </div>

        <div class="features">
            <div class="feature">
                <div class="feature-icon">🎨</div>
                <h3>خلفيات متحركة</h3>
                <p>تدرجات لونية جميلة تتغير تلقائياً</p>
            </div>
            
            <div class="feature">
                <div class="feature-icon">✨</div>
                <h3>تأثيرات بصرية</h3>
                <p>جسيمات عائمة وتوهج ناعم</p>
            </div>
            
            <div class="feature">
                <div class="feature-icon">👥</div>
                <h3>شخصيات أنمي</h3>
                <p>معلومات عن أشهر الشخصيات</p>
            </div>
            
            <div class="feature">
                <div class="feature-icon">💭</div>
                <h3>اقتباسات ملهمة</h3>
                <p>اقتباسات محفزة تتجدد تلقائياً</p>
            </div>
        </div>

        <h2>🚀 الميزات المتقدمة</h2>
        <div class="instructions">
            <div class="step">
                <strong>⚙️ صفحة الإعدادات:</strong> افتح <code>settings.html</code> لتخصيص التجربة
            </div>
            
            <div class="step">
                <strong>🎭 تأثيرات المواقع:</strong> Extension يضيف تأثيرات جميلة لجميع المواقع
            </div>
            
            <div class="step">
                <strong>🎨 أنماط الألوان:</strong> اختر من 5 أنماط ألوان مختلفة
            </div>
            
            <div class="step">
                <strong>💾 حفظ الإعدادات:</strong> جميع تفضيلاتك محفوظة تلقائياً
            </div>
        </div>

        <div style="margin: 30px 0;">
            <a href="settings.html" class="button">🔧 فتح الإعدادات</a>
            <a href="README.md" class="button">📖 قراءة الدليل</a>
            <a href="INSTALLATION.md" class="button">📦 دليل التثبيت</a>
        </div>

        <h2>🎯 نصائح للاستخدام</h2>
        <div class="instructions">
            <div class="step">
                <strong>🖱️ التفاعل:</strong> اضغط على البطاقات والأزرار لرؤية التأثيرات
            </div>
            
            <div class="step">
                <strong>🔄 تغيير الخلفية:</strong> استخدم زر "تغيير الخلفية" في قسم الخلفيات
            </div>
            
            <div class="step">
                <strong>⏸️ إيقاف الحركة:</strong> يمكنك إيقاف/تشغيل الحركات حسب الحاجة
            </div>
            
            <div class="step">
                <strong>💡 الاقتباسات:</strong> اضغط "اقتباس جديد" للحصول على اقتباس ملهم
            </div>
        </div>

        <div style="margin-top: 40px; padding-top: 20px; border-top: 1px solid rgba(255, 255, 255, 0.3);">
            <p><strong>🎉 استمتع بتجربة أنمي فريدة مع Anime Paradise!</strong></p>
            <p style="font-size: 0.9em; opacity: 0.8;">تم التطوير بـ ❤️ للمجتمع العربي</p>
        </div>
    </div>

    <script>
        // إضافة تأثيرات بصرية للصفحة
        document.addEventListener('DOMContentLoaded', function() {
            // إنشاء جسيمات عائمة
            createFloatingParticles();
            
            // تأثير المؤشر
            createCursorEffect();
        });

        function createFloatingParticles() {
            for (let i = 0; i < 20; i++) {
                const particle = document.createElement('div');
                particle.style.cssText = `
                    position: fixed;
                    width: 4px;
                    height: 4px;
                    background: radial-gradient(circle, rgba(255, 182, 193, 0.8) 0%, transparent 70%);
                    border-radius: 50%;
                    left: ${Math.random() * 100}%;
                    top: 100%;
                    pointer-events: none;
                    z-index: 1000;
                    animation: floatUp ${Math.random() * 5 + 8}s linear infinite;
                `;
                document.body.appendChild(particle);
            }
        }

        function createCursorEffect() {
            const cursor = document.createElement('div');
            cursor.style.cssText = `
                position: fixed;
                width: 20px;
                height: 20px;
                background: radial-gradient(circle, rgba(255, 182, 193, 0.3) 0%, transparent 70%);
                border-radius: 50%;
                pointer-events: none;
                z-index: 9999;
                transition: transform 0.1s ease;
                mix-blend-mode: screen;
            `;
            document.body.appendChild(cursor);

            document.addEventListener('mousemove', function(e) {
                cursor.style.left = (e.clientX - 10) + 'px';
                cursor.style.top = (e.clientY - 10) + 'px';
            });
        }

        // إضافة CSS للحركات
        const style = document.createElement('style');
        style.textContent = `
            @keyframes floatUp {
                0% {
                    transform: translateY(0) rotate(0deg);
                    opacity: 0;
                }
                10% {
                    opacity: 1;
                }
                90% {
                    opacity: 1;
                }
                100% {
                    transform: translateY(-100vh) rotate(360deg);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
