// Content Script لـ Anime Paradise Extension
// يضيف تأثيرات أنمي جميلة لجميع المواقع

(function() {
    'use strict';
    
    let isAnimeEffectsEnabled = true;
    let particlesContainer = null;
    let cursorGlow = null;
    let backgroundOverlay = null;
    
    // تهيئة التأثيرات عند تحميل الصفحة
    function initializeAnimeEffects() {
        // التحقق من إعدادات المستخدم
        chrome.storage.local.get(['animeSettings'], function(result) {
            const settings = result.animeSettings || {};
            isAnimeEffectsEnabled = settings.enableWebEffects !== false; // افتراضياً مفعل
            
            if (isAnimeEffectsEnabled) {
                createBackgroundOverlay();
                createParticleSystem();
                createCursorEffects();
                enhancePageElements();
                addClickEffects();
                addScrollEffects();
            }
        });
    }
    
    // إنشاء الخلفية المتدرجة
    function createBackgroundOverlay() {
        if (backgroundOverlay) return;
        
        backgroundOverlay = document.createElement('div');
        backgroundOverlay.className = 'anime-background-overlay';
        backgroundOverlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(
                45deg,
                rgba(255, 182, 193, 0.02) 0%,
                rgba(173, 216, 230, 0.02) 25%,
                rgba(255, 218, 185, 0.02) 50%,
                rgba(221, 160, 221, 0.02) 75%,
                rgba(255, 182, 193, 0.02) 100%
            );
            background-size: 400% 400%;
            animation: gradientShift 20s ease infinite;
            pointer-events: none;
            z-index: -1;
        `;
        
        document.body.appendChild(backgroundOverlay);
    }
    
    // إنشاء نظام الجسيمات
    function createParticleSystem() {
        if (particlesContainer) return;
        
        particlesContainer = document.createElement('div');
        particlesContainer.className = 'anime-particles';
        document.body.appendChild(particlesContainer);
        
        // إنشاء الجسيمات
        for (let i = 0; i < 15; i++) {
            createParticle();
        }
        
        // إنشاء جسيمة جديدة كل 3 ثوانٍ
        setInterval(createParticle, 3000);
    }
    
    // إنشاء جسيمة واحدة
    function createParticle() {
        if (!particlesContainer) return;
        
        const particle = document.createElement('div');
        particle.className = 'anime-particle';
        
        const size = Math.random() * 6 + 2;
        const startX = Math.random() * window.innerWidth;
        const duration = Math.random() * 5 + 8;
        const delay = Math.random() * 2;
        
        particle.style.cssText = `
            position: absolute;
            width: ${size}px;
            height: ${size}px;
            left: ${startX}px;
            bottom: -10px;
            background: radial-gradient(circle, rgba(255, 182, 193, 0.8) 0%, rgba(255, 182, 193, 0) 70%);
            border-radius: 50%;
            animation: floatUp ${duration}s linear ${delay}s forwards;
            pointer-events: none;
        `;
        
        particlesContainer.appendChild(particle);
        
        // إزالة الجسيمة بعد انتهاء الحركة
        setTimeout(() => {
            if (particle.parentNode) {
                particle.parentNode.removeChild(particle);
            }
        }, (duration + delay) * 1000);
    }
    
    // إنشاء تأثيرات المؤشر
    function createCursorEffects() {
        if (cursorGlow) return;
        
        cursorGlow = document.createElement('div');
        cursorGlow.className = 'anime-cursor-glow';
        cursorGlow.style.cssText = `
            position: fixed;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: radial-gradient(circle, rgba(255, 182, 193, 0.3) 0%, transparent 70%);
            pointer-events: none;
            z-index: 9998;
            transition: transform 0.1s ease;
            mix-blend-mode: screen;
            opacity: 0;
        `;
        
        document.body.appendChild(cursorGlow);
        
        // تتبع حركة المؤشر
        document.addEventListener('mousemove', function(e) {
            if (cursorGlow) {
                cursorGlow.style.left = (e.clientX - 15) + 'px';
                cursorGlow.style.top = (e.clientY - 15) + 'px';
                cursorGlow.style.opacity = '1';
            }
        });
        
        // إخفاء التوهج عند مغادرة الصفحة
        document.addEventListener('mouseleave', function() {
            if (cursorGlow) {
                cursorGlow.style.opacity = '0';
            }
        });
    }
    
    // تحسين عناصر الصفحة
    function enhancePageElements() {
        // تحسين الأزرار
        const buttons = document.querySelectorAll('button, input[type="submit"], input[type="button"]');
        buttons.forEach(button => {
            if (!button.classList.contains('anime-enhanced')) {
                button.classList.add('anime-button', 'anime-enhanced');
            }
        });
        
        // تحسين الروابط
        const links = document.querySelectorAll('a');
        links.forEach(link => {
            if (!link.classList.contains('anime-enhanced')) {
                link.classList.add('anime-link', 'anime-enhanced');
            }
        });
        
        // تحسين الحقول النصية
        const inputs = document.querySelectorAll('input[type="text"], input[type="email"], input[type="password"], textarea');
        inputs.forEach(input => {
            if (!input.classList.contains('anime-enhanced')) {
                input.classList.add('anime-input', 'anime-enhanced');
            }
        });
        
        // تحسين البطاقات والحاويات
        const cards = document.querySelectorAll('.card, .box, .container, .panel, .widget');
        cards.forEach(card => {
            if (!card.classList.contains('anime-enhanced')) {
                card.classList.add('anime-card', 'anime-enhanced');
            }
        });
    }
    
    // إضافة تأثيرات النقر
    function addClickEffects() {
        document.addEventListener('click', function(e) {
            createClickWave(e.clientX, e.clientY);
        });
    }
    
    // إنشاء موجة النقر
    function createClickWave(x, y) {
        const wave = document.createElement('div');
        wave.className = 'anime-click-wave';
        wave.style.cssText = `
            position: fixed;
            left: ${x - 1}px;
            top: ${y - 1}px;
            width: 2px;
            height: 2px;
            border: 2px solid rgba(255, 182, 193, 0.6);
            border-radius: 50%;
            pointer-events: none;
            z-index: 9997;
            animation: waveExpand 0.6s ease-out forwards;
        `;
        
        document.body.appendChild(wave);
        
        // إزالة الموجة بعد انتهاء الحركة
        setTimeout(() => {
            if (wave.parentNode) {
                wave.parentNode.removeChild(wave);
            }
        }, 600);
    }
    
    // إضافة تأثيرات التمرير
    function addScrollEffects() {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('anime-fade-in');
                }
            });
        }, {
            threshold: 0.1
        });
        
        // مراقبة العناصر الجديدة
        const elementsToObserve = document.querySelectorAll('h1, h2, h3, .card, .box, img, p');
        elementsToObserve.forEach(el => {
            if (!el.classList.contains('anime-observed')) {
                observer.observe(el);
                el.classList.add('anime-observed');
            }
        });
    }
    
    // مراقبة التغييرات في DOM
    function observeDOMChanges() {
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach((node) => {
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            // تحسين العناصر الجديدة
                            setTimeout(() => {
                                enhancePageElements();
                                addScrollEffects();
                            }, 100);
                        }
                    });
                }
            });
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }
    
    // تبديل التأثيرات
    function toggleAnimeEffects(enabled) {
        isAnimeEffectsEnabled = enabled;
        
        if (enabled) {
            createBackgroundOverlay();
            createParticleSystem();
            createCursorEffects();
            enhancePageElements();
        } else {
            // إزالة التأثيرات
            if (particlesContainer) {
                particlesContainer.remove();
                particlesContainer = null;
            }
            if (cursorGlow) {
                cursorGlow.remove();
                cursorGlow = null;
            }
            if (backgroundOverlay) {
                backgroundOverlay.remove();
                backgroundOverlay = null;
            }
            
            // إزالة الفئات المحسنة
            document.querySelectorAll('.anime-enhanced').forEach(el => {
                el.classList.remove('anime-button', 'anime-link', 'anime-input', 'anime-card', 'anime-enhanced');
            });
        }
    }
    
    // الاستماع للرسائل من popup
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
        if (request.action === 'toggleEffects') {
            toggleAnimeEffects(request.enabled);
            sendResponse({ success: true });
        } else if (request.action === 'createParticles') {
            for (let i = 0; i < 10; i++) {
                setTimeout(() => createParticle(), i * 100);
            }
            sendResponse({ success: true });
        }
    });
    
    // تهيئة التأثيرات عند تحميل الصفحة
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeAnimeEffects);
    } else {
        initializeAnimeEffects();
    }
    
    // مراقبة التغييرات في DOM
    observeDOMChanges();
    
    // إضافة CSS للحركات
    const style = document.createElement('style');
    style.textContent = `
        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }
        
        @keyframes floatUp {
            0% {
                transform: translateY(0) rotate(0deg);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateY(-100vh) rotate(360deg);
                opacity: 0;
            }
        }
        
        @keyframes waveExpand {
            0% {
                width: 2px;
                height: 2px;
                opacity: 1;
            }
            100% {
                width: 60px;
                height: 60px;
                opacity: 0;
            }
        }
        
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    `;
    document.head.appendChild(style);
    
})();
