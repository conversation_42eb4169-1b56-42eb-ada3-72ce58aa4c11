// إعدادات Anime Paradise Extension

document.addEventListener('DOMContentLoaded', function() {
    initializeSettings();
    loadCurrentSettings();
    attachEventListeners();
});

// الإعدادات الافتراضية
const defaultSettings = {
    enableBackgrounds: true,
    backgroundSpeed: 5,
    autoChangeInterval: 30,
    enableParticles: true,
    particleDensity: 10,
    enableCursorGlow: true,
    enableClickWaves: true,
    quoteChangeInterval: 45,
    enableNotifications: false,
    language: 'ar',
    colorTheme: 'default',
    primaryColor: '#ff6b9d',
    secondaryColor: '#4ecdc4',
    enableWebEffects: true,
    performanceMode: 'balanced',
    enableBlur: true
};

// تهيئة الإعدادات
function initializeSettings() {
    // تحديث قيم المنزلقات
    updateRangeValues();
    
    // إضافة تأثيرات بصرية
    addVisualEffects();
}

// تحميل الإعدادات الحالية
function loadCurrentSettings() {
    chrome.storage.local.get(['animeSettings'], function(result) {
        const settings = { ...defaultSettings, ...result.animeSettings };
        
        // تطبيق الإعدادات على العناصر
        Object.keys(settings).forEach(key => {
            const element = document.getElementById(key);
            if (element) {
                if (element.type === 'checkbox') {
                    element.checked = settings[key];
                } else if (element.type === 'range') {
                    element.value = settings[key];
                    updateRangeValue(element);
                } else {
                    element.value = settings[key];
                }
            }
        });
        
        // تطبيق نمط الألوان
        applyColorTheme(settings.colorTheme);
    });
}

// ربط مستمعي الأحداث
function attachEventListeners() {
    // أزرار الإجراءات
    document.getElementById('saveSettings').addEventListener('click', saveSettings);
    document.getElementById('resetSettings').addEventListener('click', resetSettings);
    document.getElementById('exportSettings').addEventListener('click', exportSettings);
    document.getElementById('importSettings').addEventListener('click', importSettings);
    
    // المنزلقات
    const rangeInputs = document.querySelectorAll('input[type="range"]');
    rangeInputs.forEach(input => {
        input.addEventListener('input', function() {
            updateRangeValue(this);
        });
    });
    
    // نمط الألوان
    document.getElementById('colorTheme').addEventListener('change', function() {
        applyColorTheme(this.value);
    });
    
    // الألوان المخصصة
    document.getElementById('primaryColor').addEventListener('change', updateCustomColors);
    document.getElementById('secondaryColor').addEventListener('change', updateCustomColors);
    
    // معاينة فورية للتأثيرات
    document.getElementById('enableParticles').addEventListener('change', previewParticles);
    document.getElementById('particleDensity').addEventListener('input', previewParticles);
}

// تحديث قيم المنزلقات
function updateRangeValues() {
    const rangeInputs = document.querySelectorAll('input[type="range"]');
    rangeInputs.forEach(input => {
        updateRangeValue(input);
    });
}

function updateRangeValue(input) {
    const valueSpan = input.parentNode.querySelector('.range-value');
    if (valueSpan) {
        valueSpan.textContent = input.value;
    }
}

// حفظ الإعدادات
function saveSettings() {
    const saveButton = document.getElementById('saveSettings');
    saveButton.classList.add('loading');
    saveButton.textContent = 'جاري الحفظ...';
    
    const settings = {};
    
    // جمع جميع الإعدادات
    Object.keys(defaultSettings).forEach(key => {
        const element = document.getElementById(key);
        if (element) {
            if (element.type === 'checkbox') {
                settings[key] = element.checked;
            } else if (element.type === 'range' || element.type === 'number') {
                settings[key] = parseInt(element.value);
            } else {
                settings[key] = element.value;
            }
        }
    });
    
    // حفظ في التخزين
    chrome.storage.local.set({ animeSettings: settings }, function() {
        // إرسال رسالة للتحديث
        chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
            if (tabs[0]) {
                chrome.tabs.sendMessage(tabs[0].id, {
                    action: 'updateSettings',
                    settings: settings
                });
            }
        });
        
        // إظهار رسالة نجاح
        showNotification('تم حفظ الإعدادات بنجاح!', 'success');
        
        // إعادة تعيين الزر
        setTimeout(() => {
            saveButton.classList.remove('loading');
            saveButton.textContent = 'حفظ الإعدادات';
        }, 1000);
    });
}

// إعادة تعيين الإعدادات
function resetSettings() {
    if (confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات؟')) {
        Object.keys(defaultSettings).forEach(key => {
            const element = document.getElementById(key);
            if (element) {
                if (element.type === 'checkbox') {
                    element.checked = defaultSettings[key];
                } else if (element.type === 'range') {
                    element.value = defaultSettings[key];
                    updateRangeValue(element);
                } else {
                    element.value = defaultSettings[key];
                }
            }
        });
        
        applyColorTheme('default');
        showNotification('تم إعادة تعيين الإعدادات!', 'info');
    }
}

// تصدير الإعدادات
function exportSettings() {
    chrome.storage.local.get(['animeSettings'], function(result) {
        const settings = result.animeSettings || defaultSettings;
        const dataStr = JSON.stringify(settings, null, 2);
        const dataBlob = new Blob([dataStr], {type: 'application/json'});
        
        const link = document.createElement('a');
        link.href = URL.createObjectURL(dataBlob);
        link.download = 'anime-paradise-settings.json';
        link.click();
        
        showNotification('تم تصدير الإعدادات!', 'success');
    });
}

// استيراد الإعدادات
function importSettings() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    
    input.onchange = function(event) {
        const file = event.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const settings = JSON.parse(e.target.result);
                    
                    // التحقق من صحة الإعدادات
                    const validSettings = {};
                    Object.keys(defaultSettings).forEach(key => {
                        if (settings.hasOwnProperty(key)) {
                            validSettings[key] = settings[key];
                        } else {
                            validSettings[key] = defaultSettings[key];
                        }
                    });
                    
                    // تطبيق الإعدادات
                    chrome.storage.local.set({ animeSettings: validSettings }, function() {
                        loadCurrentSettings();
                        showNotification('تم استيراد الإعدادات بنجاح!', 'success');
                    });
                    
                } catch (error) {
                    showNotification('خطأ في قراءة ملف الإعدادات!', 'error');
                }
            };
            reader.readAsText(file);
        }
    };
    
    input.click();
}

// تطبيق نمط الألوان
function applyColorTheme(theme) {
    const root = document.documentElement;
    
    const themes = {
        default: {
            primary: '#ff6b9d',
            secondary: '#4ecdc4',
            gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
        },
        sakura: {
            primary: '#ffb7c5',
            secondary: '#ffc0cb',
            gradient: 'linear-gradient(135deg, #ffb7c5 0%, #ffc0cb 100%)'
        },
        ocean: {
            primary: '#4ecdc4',
            secondary: '#44a08d',
            gradient: 'linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%)'
        },
        sunset: {
            primary: '#ff9a9e',
            secondary: '#fecfef',
            gradient: 'linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)'
        },
        galaxy: {
            primary: '#667eea',
            secondary: '#764ba2',
            gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
        }
    };
    
    const selectedTheme = themes[theme] || themes.default;
    
    root.style.setProperty('--primary-color', selectedTheme.primary);
    root.style.setProperty('--secondary-color', selectedTheme.secondary);
    document.body.style.background = selectedTheme.gradient;
}

// تحديث الألوان المخصصة
function updateCustomColors() {
    const primaryColor = document.getElementById('primaryColor').value;
    const secondaryColor = document.getElementById('secondaryColor').value;
    
    document.documentElement.style.setProperty('--primary-color', primaryColor);
    document.documentElement.style.setProperty('--secondary-color', secondaryColor);
    
    // تحديث الخلفية
    document.body.style.background = `linear-gradient(135deg, ${primaryColor} 0%, ${secondaryColor} 100%)`;
}

// معاينة الجسيمات
function previewParticles() {
    const enableParticles = document.getElementById('enableParticles').checked;
    const density = document.getElementById('particleDensity').value;
    
    if (enableParticles) {
        createPreviewParticles(density);
    } else {
        removePreviewParticles();
    }
}

function createPreviewParticles(density) {
    removePreviewParticles();
    
    const container = document.createElement('div');
    container.id = 'preview-particles';
    container.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        pointer-events: none;
        z-index: 1000;
    `;
    
    for (let i = 0; i < density; i++) {
        const particle = document.createElement('div');
        particle.style.cssText = `
            position: absolute;
            width: 4px;
            height: 4px;
            background: radial-gradient(circle, rgba(255, 182, 193, 0.8) 0%, transparent 70%);
            border-radius: 50%;
            left: ${Math.random() * 100}%;
            top: ${Math.random() * 100}%;
            animation: float ${Math.random() * 3 + 2}s ease-in-out infinite;
        `;
        container.appendChild(particle);
    }
    
    document.body.appendChild(container);
    
    // إزالة المعاينة بعد 3 ثوانٍ
    setTimeout(removePreviewParticles, 3000);
}

function removePreviewParticles() {
    const existing = document.getElementById('preview-particles');
    if (existing) {
        existing.remove();
    }
}

// إضافة تأثيرات بصرية
function addVisualEffects() {
    // إضافة CSS للحركات
    const style = document.createElement('style');
    style.textContent = `
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
    `;
    document.head.appendChild(style);
}

// إظهار الإشعارات
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px 25px;
        background: ${type === 'success' ? '#4caf50' : type === 'error' ? '#f44336' : '#2196f3'};
        color: white;
        border-radius: 10px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        z-index: 10000;
        animation: slideIn 0.3s ease;
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.style.animation = 'slideOut 0.3s ease forwards';
        setTimeout(() => notification.remove(), 300);
    }, 3000);
}

// إضافة CSS للإشعارات
const notificationStyle = document.createElement('style');
notificationStyle.textContent = `
    @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    
    @keyframes slideOut {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
`;
document.head.appendChild(notificationStyle);
