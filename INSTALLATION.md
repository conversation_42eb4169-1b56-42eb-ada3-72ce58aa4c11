# 📦 دليل تثبيت Anime Paradise Extension

## متطلبات النظام

- **متصفح جوجل كروم** الإصدار 88 أو أحدث
- **نظام التشغيل**: Windows 10/11, macOS 10.14+, Linux Ubuntu 18.04+
- **ذاكرة الوصول العشوائي**: 4 جيجابايت على الأقل
- **مساحة التخزين**: 50 ميجابايت متاحة

## طرق التثبيت

### الطريقة الأولى: التثبيت المباشر (موصى بها)

1. **تحميل الملفات**
   ```bash
   # استنساخ المستودع
   git clone https://github.com/your-username/anime-paradise-extension.git
   
   # أو تحميل ملف ZIP
   # انقر على "Code" ثم "Download ZIP"
   ```

2. **فتح إعدادات Extensions في Chrome**
   - افتح متصفح جوجل كروم
   - اذهب إلى `chrome://extensions/`
   - أو من القائمة: `المزيد من الأدوات` > `الإضافات`

3. **تفعيل وضع المطور**
   - في أعلى يمين الصفحة، فعّل "وضع المطور" (Developer mode)

4. **تحميل Extension**
   - اضغط على "تحميل extension غير مُعبأ" (Load unpacked)
   - اختر مجلد `anime-paradise-extension`
   - اضغط "اختيار مجلد" (Select Folder)

5. **التحقق من التثبيت**
   - ستظهر أيقونة Anime Paradise في شريط الأدوات
   - اضغط على الأيقونة لفتح التطبيق

### الطريقة الثانية: التثبيت من Chrome Web Store (قريباً)

```
🚧 قيد التطوير - سيتم نشر Extension في Chrome Web Store قريباً
```

## إعداد الأيقونات

نظراً لأن الأيقونات غير متوفرة في المستودع، يمكنك:

### الخيار الأول: إنشاء أيقونات مخصصة
1. استخدم أدوات التصميم مثل:
   - [Figma](https://figma.com) (مجاني)
   - [Canva](https://canva.com) (مجاني)
   - Adobe Illustrator
   - GIMP (مجاني ومفتوح المصدر)

2. أنشئ أيقونات بالأحجام التالية:
   - `icon16.png` (16x16 بكسل)
   - `icon32.png` (32x32 بكسل)
   - `icon48.png` (48x48 بكسل)
   - `icon128.png` (128x128 بكسل)

3. احفظ الأيقونات في مجلد `icons/`

### الخيار الثاني: استخدام أيقونات مؤقتة
1. ابحث عن أيقونات أنمي مجانية من:
   - [Flaticon](https://flaticon.com)
   - [Icons8](https://icons8.com)
   - [Freepik](https://freepik.com)

2. تأكد من الترخيص المناسب للاستخدام

### الخيار الثالث: أيقونات نصية مؤقتة
يمكنك إنشاء أيقونات نصية بسيطة باستخدام الرموز التعبيرية:

```html
<!-- مثال لأيقونة نصية -->
<div style="width: 128px; height: 128px; background: linear-gradient(45deg, #ff6b9d, #4ecdc4); 
            display: flex; align-items: center; justify-content: center; 
            font-size: 64px; border-radius: 20px;">🌸</div>
```

## استكشاف الأخطاء وإصلاحها

### المشكلة: Extension لا يظهر في شريط الأدوات
**الحل:**
1. تأكد من تفعيل Extension في `chrome://extensions/`
2. اضغط على أيقونة الإضافات (puzzle piece) في شريط الأدوات
3. ثبت Anime Paradise بالضغط على أيقونة الدبوس

### المشكلة: خطأ في تحميل الأيقونات
**الحل:**
1. تأكد من وجود ملفات الأيقونات في مجلد `icons/`
2. تحقق من أسماء الملفات (icon16.png, icon32.png, إلخ)
3. تأكد من صيغة الملفات (PNG موصى بها)

### المشكلة: التأثيرات لا تعمل على بعض المواقع
**الحل:**
1. تحقق من إعدادات Extension
2. بعض المواقع قد تحجب Content Scripts
3. جرب إعادة تحميل الصفحة

### المشكلة: بطء في الأداء
**الحل:**
1. افتح إعدادات Extension
2. قلل من كثافة الجسيمات
3. غيّر وضع الأداء إلى "أداء عالي"
4. أوقف التأثيرات على المواقع إذا لزم الأمر

## التحديثات

### التحديث اليدوي
1. حمّل أحدث إصدار من المستودع
2. في `chrome://extensions/`، اضغط على أيقونة التحديث بجانب Extension
3. أو أزل Extension القديم وثبت الجديد

### التحديث التلقائي (قريباً)
عند نشر Extension في Chrome Web Store، ستحصل على تحديثات تلقائية.

## إلغاء التثبيت

1. اذهب إلى `chrome://extensions/`
2. ابحث عن "Anime Paradise"
3. اضغط على "إزالة" (Remove)
4. أكد الإزالة

## الدعم الفني

### قبل طلب المساعدة
1. تأكد من اتباع تعليمات التثبيت بدقة
2. جرب إعادة تشغيل المتصفح
3. تحقق من وجود تحديثات للمتصفح

### طلب المساعدة
- **GitHub Issues**: [رابط المستودع]/issues
- **البريد الإلكتروني**: <EMAIL>
- **Discord**: [رابط الخادم]

### معلومات مفيدة عند طلب المساعدة
- إصدار المتصفح
- نظام التشغيل
- وصف المشكلة بالتفصيل
- لقطات شاشة (إن أمكن)
- رسائل الخطأ في Console

## نصائح للاستخدام الأمثل

1. **الأداء**: أوقف التأثيرات على المواقع الثقيلة
2. **البطارية**: قلل كثافة التأثيرات على الأجهزة المحمولة
3. **التخصيص**: جرب أنماط الألوان المختلفة
4. **المحتوى**: أضف شخصياتك واقتباساتك المفضلة

## الأمان والخصوصية

- Extension يعمل محلياً ولا يرسل بيانات لخوادم خارجية
- جميع الإعدادات محفوظة محلياً في المتصفح
- لا يتم جمع أي معلومات شخصية
- الكود مفتوح المصدر ويمكن مراجعته

---

**🎉 مبروك! أصبح لديك الآن Anime Paradise Extension جاهز للاستخدام!**

للمزيد من المساعدة، راجع [README.md](README.md) أو اتصل بفريق الدعم.
