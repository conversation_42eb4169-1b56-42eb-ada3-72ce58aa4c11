/* تأثيرات الأنمي للمواقع */

/* تأثير الجسيمات العائمة */
.anime-particles {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 9999;
    overflow: hidden;
}

.anime-particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: radial-gradient(circle, rgba(255, 182, 193, 0.8) 0%, rgba(255, 182, 193, 0) 70%);
    border-radius: 50%;
    animation: floatUp 8s linear infinite;
}

.anime-particle:nth-child(2n) {
    background: radial-gradient(circle, rgba(173, 216, 230, 0.8) 0%, rgba(173, 216, 230, 0) 70%);
    animation-duration: 10s;
}

.anime-particle:nth-child(3n) {
    background: radial-gradient(circle, rgba(255, 218, 185, 0.8) 0%, rgba(255, 218, 185, 0) 70%);
    animation-duration: 12s;
}

.anime-particle:nth-child(4n) {
    background: radial-gradient(circle, rgba(221, 160, 221, 0.8) 0%, rgba(221, 160, 221, 0) 70%);
    animation-duration: 9s;
}

@keyframes floatUp {
    0% {
        transform: translateY(100vh) rotate(0deg);
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        transform: translateY(-100px) rotate(360deg);
        opacity: 0;
    }
}

/* تأثير الهالة حول المؤشر */
.anime-cursor-glow {
    position: fixed;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(255, 182, 193, 0.3) 0%, transparent 70%);
    pointer-events: none;
    z-index: 9998;
    transition: transform 0.1s ease;
    mix-blend-mode: screen;
}

/* تأثير الموجات عند النقر */
.anime-click-wave {
    position: fixed;
    border: 2px solid rgba(255, 182, 193, 0.6);
    border-radius: 50%;
    pointer-events: none;
    z-index: 9997;
    animation: waveExpand 0.6s ease-out forwards;
}

@keyframes waveExpand {
    0% {
        width: 0;
        height: 0;
        opacity: 1;
    }
    100% {
        width: 60px;
        height: 60px;
        opacity: 0;
    }
}

/* تأثير الخلفية المتدرجة الناعمة */
.anime-background-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        45deg,
        rgba(255, 182, 193, 0.02) 0%,
        rgba(173, 216, 230, 0.02) 25%,
        rgba(255, 218, 185, 0.02) 50%,
        rgba(221, 160, 221, 0.02) 75%,
        rgba(255, 182, 193, 0.02) 100%
    );
    background-size: 400% 400%;
    animation: gradientShift 20s ease infinite;
    pointer-events: none;
    z-index: -1;
}

/* تأثير الإضاءة الناعمة للعناصر */
.anime-glow-element {
    box-shadow: 0 0 20px rgba(255, 182, 193, 0.3) !important;
    transition: box-shadow 0.3s ease !important;
}

.anime-glow-element:hover {
    box-shadow: 0 0 30px rgba(255, 182, 193, 0.5) !important;
}

/* تأثير النص المتوهج */
.anime-text-glow {
    text-shadow: 
        0 0 5px rgba(255, 182, 193, 0.5),
        0 0 10px rgba(255, 182, 193, 0.3),
        0 0 15px rgba(255, 182, 193, 0.2) !important;
}

/* تأثير الحدود المتوهجة */
.anime-border-glow {
    border: 1px solid rgba(255, 182, 193, 0.5) !important;
    box-shadow: 
        inset 0 0 10px rgba(255, 182, 193, 0.2),
        0 0 10px rgba(255, 182, 193, 0.3) !important;
}

/* تأثير الأزرار الأنيقة */
button.anime-button,
input[type="submit"].anime-button,
input[type="button"].anime-button,
.anime-button {
    background: linear-gradient(45deg, rgba(255, 182, 193, 0.8), rgba(173, 216, 230, 0.8)) !important;
    border: none !important;
    border-radius: 25px !important;
    color: white !important;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3) !important;
    transition: all 0.3s ease !important;
    position: relative !important;
    overflow: hidden !important;
}

button.anime-button:hover,
input[type="submit"].anime-button:hover,
input[type="button"].anime-button:hover,
.anime-button:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 5px 15px rgba(255, 182, 193, 0.4) !important;
}

button.anime-button::before,
input[type="submit"].anime-button::before,
input[type="button"].anime-button::before,
.anime-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s ease;
}

button.anime-button:hover::before,
input[type="submit"].anime-button:hover::before,
input[type="button"].anime-button:hover::before,
.anime-button:hover::before {
    left: 100%;
}

/* تأثير الروابط الأنيقة */
a.anime-link {
    color: #ff6b9d !important;
    text-decoration: none !important;
    position: relative !important;
    transition: color 0.3s ease !important;
}

a.anime-link::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, #ff6b9d, #4ecdc4);
    transition: width 0.3s ease;
}

a.anime-link:hover {
    color: #4ecdc4 !important;
}

a.anime-link:hover::after {
    width: 100%;
}

/* تأثير البطاقات الأنيقة */
.anime-card {
    background: rgba(255, 255, 255, 0.9) !important;
    backdrop-filter: blur(10px) !important;
    border-radius: 15px !important;
    border: 1px solid rgba(255, 182, 193, 0.3) !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1) !important;
    transition: all 0.3s ease !important;
}

.anime-card:hover {
    transform: translateY(-5px) !important;
    box-shadow: 0 15px 40px rgba(255, 182, 193, 0.2) !important;
}

/* تأثير الحقول النصية */
input[type="text"].anime-input,
input[type="email"].anime-input,
input[type="password"].anime-input,
textarea.anime-input,
.anime-input {
    background: rgba(255, 255, 255, 0.9) !important;
    border: 2px solid rgba(255, 182, 193, 0.3) !important;
    border-radius: 10px !important;
    padding: 10px 15px !important;
    transition: all 0.3s ease !important;
    backdrop-filter: blur(5px) !important;
}

input[type="text"].anime-input:focus,
input[type="email"].anime-input:focus,
input[type="password"].anime-input:focus,
textarea.anime-input:focus,
.anime-input:focus {
    border-color: rgba(255, 182, 193, 0.8) !important;
    box-shadow: 0 0 15px rgba(255, 182, 193, 0.3) !important;
    outline: none !important;
}

/* تأثير التمرير المخصص */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 182, 193, 0.1);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(45deg, rgba(255, 182, 193, 0.6), rgba(173, 216, 230, 0.6));
    border-radius: 4px;
    transition: background 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(45deg, rgba(255, 182, 193, 0.8), rgba(173, 216, 230, 0.8));
}

/* تأثير الظلال الناعمة */
.anime-soft-shadow {
    box-shadow: 
        0 2px 10px rgba(255, 182, 193, 0.1),
        0 4px 20px rgba(173, 216, 230, 0.1),
        0 8px 40px rgba(255, 218, 185, 0.1) !important;
}

/* تأثير الحركة للعناصر عند الظهور */
.anime-fade-in {
    animation: fadeInUp 0.6s ease forwards;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* تأثير النبضة للعناصر المهمة */
.anime-pulse {
    animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.05);
        opacity: 0.8;
    }
}

/* إخفاء التأثيرات عند الطباعة */
@media print {
    .anime-particles,
    .anime-cursor-glow,
    .anime-click-wave,
    .anime-background-overlay {
        display: none !important;
    }
}
