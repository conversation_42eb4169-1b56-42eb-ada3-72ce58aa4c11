// خدمة الخلفية لـ Anime Paradise Extension

// تهيئة التطبيق عند التثبيت
chrome.runtime.onInstalled.addListener((details) => {
    if (details.reason === 'install') {
        // إعدادات افتراضية عند التثبيت لأول مرة
        const defaultSettings = {
            currentWallpaper: 0,
            animationPaused: false,
            autoChangeWallpaper: true,
            autoChangeInterval: 30000, // 30 ثانية
            favoriteCharacters: [],
            favoriteQuotes: [],
            theme: 'default',
            language: 'ar'
        };
        
        chrome.storage.local.set({ 
            animeSettings: defaultSettings,
            installDate: Date.now()
        });
        
        // فتح صفحة الترحيب
        chrome.tabs.create({
            url: chrome.runtime.getURL('welcome.html')
        });
        
        console.log('Anime Paradise Extension installed successfully!');
    } else if (details.reason === 'update') {
        console.log('Anime Paradise Extension updated to version', chrome.runtime.getManifest().version);
    }
});

// التعامل مع النقر على أيقونة التطبيق
chrome.action.onClicked.addListener((tab) => {
    // فتح النافذة المنبثقة (يتم التعامل معها تلقائياً بواسطة manifest.json)
    console.log('Extension icon clicked');
});

// إدارة الرسائل من content scripts و popup
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    switch (request.action) {
        case 'getSettings':
            chrome.storage.local.get(['animeSettings'], (result) => {
                sendResponse(result.animeSettings || {});
            });
            return true; // للاستجابة غير المتزامنة
            
        case 'saveSettings':
            chrome.storage.local.set({ animeSettings: request.settings }, () => {
                sendResponse({ success: true });
            });
            return true;
            
        case 'getQuote':
            getRandomQuote().then(quote => {
                sendResponse(quote);
            });
            return true;
            
        case 'getCharacterInfo':
            getCharacterInfo(request.characterName).then(info => {
                sendResponse(info);
            });
            return true;
            
        case 'addToFavorites':
            addToFavorites(request.type, request.item).then(result => {
                sendResponse(result);
            });
            return true;
            
        case 'removeFromFavorites':
            removeFromFavorites(request.type, request.itemId).then(result => {
                sendResponse(result);
            });
            return true;
            
        default:
            sendResponse({ error: 'Unknown action' });
    }
});

// دالة للحصول على اقتباس عشوائي
async function getRandomQuote() {
    const quotes = [
        {
            id: 1,
            text: "الأحلام لا تصبح حقيقة من خلال السحر؛ إنها تحتاج إلى العرق والتصميم والعمل الشاق",
            author: "كولين باول",
            category: "motivation"
        },
        {
            id: 2,
            text: "لا تستسلم أبداً، لأن هذا هو المكان والوقت الذي ستتغير فيه الأمور",
            author: "هارييت بيتشر ستو",
            category: "perseverance"
        },
        {
            id: 3,
            text: "النجاح ليس نهائياً، والفشل ليس قاتلاً: إنها الشجاعة للاستمرار هي التي تهم",
            author: "ونستون تشرشل",
            category: "success"
        },
        {
            id: 4,
            text: "الطريقة الوحيدة للقيام بعمل عظيم هي أن تحب ما تفعله",
            author: "ستيف جوبز",
            category: "passion"
        },
        {
            id: 5,
            text: "المستقبل ينتمي لأولئك الذين يؤمنون بجمال أحلامهم",
            author: "إليانور روزفلت",
            category: "dreams"
        },
        {
            id: 6,
            text: "القوة الحقيقية لا تأتي من القدرات الجسدية، بل من الإرادة التي لا تقهر",
            author: "مهاتما غاندي",
            category: "strength"
        },
        {
            id: 7,
            text: "لا تخف من التخلي عن الجيد للحصول على العظيم",
            author: "جون د. روكفلر",
            category: "courage"
        },
        {
            id: 8,
            text: "الإبداع هو الذكاء وهو يستمتع",
            author: "ألبرت أينشتاين",
            category: "creativity"
        }
    ];
    
    const randomIndex = Math.floor(Math.random() * quotes.length);
    return quotes[randomIndex];
}

// دالة للحصول على معلومات الشخصية
async function getCharacterInfo(characterName) {
    const characters = {
        'naruto': {
            name: 'ناروتو أوزوماكي',
            anime: 'ناروتو',
            description: 'نينجا شاب يحلم بأن يصبح هوكاجي قريته',
            abilities: ['راسينجان', 'وضع الحكيم', 'كوراما'],
            personality: 'مفعم بالحيوية، مصمم، مخلص لأصدقائه'
        },
        'tanjiro': {
            name: 'تانجيرو كامادو',
            anime: 'قاتل الشياطين',
            description: 'صياد شياطين يسعى لإنقاذ أخته نيزوكو',
            abilities: ['تنفس الماء', 'تنفس الشمس', 'رأس صلب'],
            personality: 'طيب القلب، شجاع، متعاطف حتى مع الشياطين'
        },
        'natsu': {
            name: 'ناتسو دراجنيل',
            anime: 'فيري تيل',
            description: 'ساحر تنين النار في نقابة فيري تيل',
            abilities: ['سحر النار', 'وضع التنين', 'قوة الصداقة'],
            personality: 'متهور، مخلص، يحب القتال والطعام'
        }
    };
    
    return characters[characterName.toLowerCase()] || null;
}

// إضافة عنصر للمفضلة
async function addToFavorites(type, item) {
    try {
        const result = await chrome.storage.local.get(['animeSettings']);
        const settings = result.animeSettings || {};
        
        if (!settings.favoriteCharacters) settings.favoriteCharacters = [];
        if (!settings.favoriteQuotes) settings.favoriteQuotes = [];
        
        if (type === 'character') {
            if (!settings.favoriteCharacters.find(char => char.id === item.id)) {
                settings.favoriteCharacters.push(item);
            }
        } else if (type === 'quote') {
            if (!settings.favoriteQuotes.find(quote => quote.id === item.id)) {
                settings.favoriteQuotes.push(item);
            }
        }
        
        await chrome.storage.local.set({ animeSettings: settings });
        return { success: true };
    } catch (error) {
        return { success: false, error: error.message };
    }
}

// إزالة عنصر من المفضلة
async function removeFromFavorites(type, itemId) {
    try {
        const result = await chrome.storage.local.get(['animeSettings']);
        const settings = result.animeSettings || {};
        
        if (type === 'character' && settings.favoriteCharacters) {
            settings.favoriteCharacters = settings.favoriteCharacters.filter(char => char.id !== itemId);
        } else if (type === 'quote' && settings.favoriteQuotes) {
            settings.favoriteQuotes = settings.favoriteQuotes.filter(quote => quote.id !== itemId);
        }
        
        await chrome.storage.local.set({ animeSettings: settings });
        return { success: true };
    } catch (error) {
        return { success: false, error: error.message };
    }
}

// تحديث البيانات دورياً
function scheduleDataUpdate() {
    // تحديث الاقتباسات كل ساعة
    setInterval(async () => {
        console.log('Updating quotes...');
        // يمكن إضافة منطق لجلب اقتباسات جديدة من API
    }, 3600000); // ساعة واحدة
    
    // تحديث معلومات الشخصيات كل يوم
    setInterval(async () => {
        console.log('Updating character data...');
        // يمكن إضافة منطق لجلب معلومات محدثة عن الشخصيات
    }, 86400000); // 24 ساعة
}

// بدء جدولة التحديثات
scheduleDataUpdate();

// التعامل مع أخطاء التطبيق
chrome.runtime.onSuspend.addListener(() => {
    console.log('Anime Paradise Extension is being suspended');
});

chrome.runtime.onStartup.addListener(() => {
    console.log('Anime Paradise Extension started');
});

// إشعارات للمستخدم (اختيارية)
function showNotification(title, message, iconUrl = 'icons/icon48.png') {
    chrome.notifications.create({
        type: 'basic',
        iconUrl: iconUrl,
        title: title,
        message: message
    });
}

// مثال على استخدام الإشعارات
chrome.alarms.onAlarm.addListener((alarm) => {
    if (alarm.name === 'dailyQuote') {
        getRandomQuote().then(quote => {
            showNotification(
                'اقتباس اليوم',
                `"${quote.text}" - ${quote.author}`
            );
        });
    }
});

// إنشاء منبه يومي للاقتباسات
chrome.alarms.create('dailyQuote', {
    delayInMinutes: 1,
    periodInMinutes: 1440 // كل 24 ساعة
});
