# 🌸 Anime Paradise - أنمي بارادايس

## وصف التطبيق

**Anime Paradise** هو extension حديث ومميز لمتصفح جوجل كروم يجلب لك تجربة أنمي فريدة مع:

- 🎨 **خلفيات متحركة جميلة** - تتغير تلقائياً مع تدرجات لونية أرستقراطية
- ✨ **تأثيرات بصرية أنيقة** - جسيمات عائمة وتوهج ناعم
- 👥 **شخصيات أنمي مفضلة** - معلومات عن أشهر شخصيات الأنمي
- 💭 **اقتباسات ملهمة** - اقتباسات محفزة تتجدد تلقائياً
- 🖼️ **معرض خلفيات** - مجموعة متنوعة من الخلفيات المتحركة
- ⚙️ **إعدادات قابلة للتخصيص** - تحكم كامل في التأثيرات

## المميزات الرئيسية

### 🎭 واجهة أنيقة وحديثة
- تصميم أرستقراطي بألوان متدرجة جميلة
- خطوط عربية أنيقة مع دعم كامل للغة العربية
- تأثيرات حركية ناعمة ومتطورة

### 🌟 تأثيرات بصرية متقدمة
- خلفيات متحركة بتدرجات لونية متغيرة
- جسيمات عائمة بألوان زاهية
- تأثيرات المؤشر مع هالة متوهجة
- موجات تفاعلية عند النقر

### 📱 تجربة مستخدم متميزة
- تنقل سلس بين الأقسام
- حفظ تلقائي للإعدادات
- تحديث دوري للمحتوى
- دعم كامل للأجهزة المختلفة

## طريقة التثبيت

### 1. تحميل الملفات
```bash
git clone https://github.com/your-username/anime-paradise-extension.git
```

### 2. تثبيت Extension في Chrome
1. افتح متصفح جوجل كروم
2. اذهب إلى `chrome://extensions/`
3. فعّل "وضع المطور" (Developer mode)
4. اضغط على "تحميل extension غير مُعبأ" (Load unpacked)
5. اختر مجلد المشروع

### 3. الاستمتاع بالتجربة
- اضغط على أيقونة Extension في شريط الأدوات
- استكشف الأقسام المختلفة
- خصص الإعدادات حسب تفضيلاتك

## بنية المشروع

```
anime-paradise-extension/
├── manifest.json          # ملف التكوين الرئيسي
├── popup.html             # واجهة Extension الرئيسية
├── background.js          # خدمة الخلفية
├── content.js             # سكريبت المحتوى المحقون
├── content.css            # تنسيقات المحتوى المحقون
├── styles/
│   └── popup.css          # تنسيقات الواجهة الرئيسية
├── scripts/
│   └── popup.js           # منطق الواجهة الرئيسية
├── icons/                 # أيقونات التطبيق
├── assets/                # الموارد الإضافية
└── README.md              # هذا الملف
```

## الأقسام الرئيسية

### 🏠 الرئيسية
- ترحيب بالمستخدم
- إحصائيات سريعة
- نظرة عامة على المحتوى

### 👥 الشخصيات
- معلومات عن شخصيات الأنمي المفضلة
- تفاصيل القدرات والشخصية
- إمكانية إضافة للمفضلة

### 🖼️ الخلفيات
- مجموعة متنوعة من الخلفيات المتحركة
- تحكم في سرعة الحركة
- تغيير تلقائي أو يدوي

### 💭 الاقتباسات
- اقتباسات ملهمة ومحفزة
- تجديد تلقائي كل فترة
- إمكانية حفظ المفضلة

## التخصيص والإعدادات

### إعدادات الخلفية
- تغيير نوع الخلفية
- تحكم في سرعة الحركة
- إيقاف/تشغيل التأثيرات

### إعدادات التأثيرات
- كثافة الجسيمات العائمة
- شدة التوهج
- تأثيرات المؤشر

### إعدادات المحتوى
- تكرار تغيير الاقتباسات
- اللغة المفضلة
- المحتوى المفضل

## التقنيات المستخدمة

- **HTML5** - بنية الواجهة
- **CSS3** - التصميم والتأثيرات البصرية
- **JavaScript ES6+** - المنطق والتفاعل
- **Chrome Extension API** - تكامل مع المتصفح
- **Web Animations API** - الحركات المتقدمة

## المتطلبات

- متصفح جوجل كروم (الإصدار 88 أو أحدث)
- دعم JavaScript
- ذاكرة متاحة: 50 ميجابايت

## الدعم والمساهمة

### الإبلاغ عن المشاكل
إذا واجهت أي مشكلة، يرجى إنشاء issue جديد مع:
- وصف المشكلة
- خطوات إعادة الإنتاج
- لقطة شاشة (إن أمكن)

### المساهمة في التطوير
نرحب بمساهماتكم! يرجى:
1. عمل Fork للمشروع
2. إنشاء branch جديد للميزة
3. إجراء التغييرات
4. إرسال Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## الشكر والتقدير

- شكر خاص لمجتمع الأنمي العربي
- مصممي الأيقونات والرسوم
- مطوري Chrome Extension API

## التحديثات القادمة

- 🎵 إضافة موسيقى خلفية
- 🎮 ألعاب أنمي صغيرة
- 📊 إحصائيات مفصلة
- 🌐 دعم لغات إضافية
- 📱 تطبيق موبايل مصاحب

---

**استمتع بتجربة أنمي فريدة مع Anime Paradise! 🌸**
